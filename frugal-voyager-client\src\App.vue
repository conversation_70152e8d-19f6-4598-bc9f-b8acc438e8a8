<script setup lang="ts">
import { ref, provide } from "vue";
import NotificationPopup from "@/components/NotificationPopup/NotificationPopup.vue";
import { useNotification } from "@/composables/useNotification";
import {
  notificationState,
  closeNotification,
  showInfo,
  showSuccess,
  showWarning,
  showError,
} from "@/services/notification-service";
import { useRouter } from "vue-router";

// 创建引用，供子组件使用
const mainRef = ref<HTMLElement | null>(null);
const headerRef = ref<HTMLElement | null>(null);

// 提供这些引用给子组件
provide("mainRef", mainRef);
provide("headerRef", headerRef);

// 使用通知组件
const { notification, hideNotification } = useNotification();

const router = useRouter();

const showSettings = () => {
  router.push("/settings");
};

const showHome = () => {
  router.push("/");
};

// 提供通知方法给子组件
provide("notification", {
  info: showInfo,
  success: showSuccess,
  warning: showWarning,
  error: showError,
});
</script>

<template>
  <div class="container">
    <div class="main" ref="mainRef">
      <header ref="headerRef">
        <div class="logo">
          <img
            src="data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
            alt="穷游旅行"
          />
          <span style="margin-left: 8px; color: #1a365d; font-weight: bold"
            >穷游旅行</span
          >
        </div>
        <div class="nav-links">
          <a href="#" @click="showHome">首页</a>
          <!-- <a href="#" @click="showPriceInfo">价格</a> -->
          <a href="#" @click="showSettings">设置</a>
          <a href="#" class="login-btn">登录</a>
        </div>
      </header>

      <div class="content">
        <RouterView />
      </div>
    </div>

    <!-- 通知弹窗组件 -->
    <NotificationPopup
      :show="notification.show"
      :type="notification.type"
      :title="notification.title"
      :message="notification.message"
      :duration="notification.duration"
      :position="notification.position"
      @close="hideNotification"
    />

    <!-- 全局通知弹窗组件 -->
    <NotificationPopup
      :show="notificationState.show"
      :type="notificationState.type"
      :title="notificationState.title"
      :message="notificationState.message"
      :duration="notificationState.duration"
      :position="notificationState.position"
      @close="closeNotification"
    />
  </div>
</template>

<style lang="scss">
.main {
  background: linear-gradient(
    to bottom right,
    #6f7985 0%,
    /* 浅蓝色，占比更多 */ #909caa 20%,
    /* 浅蓝色，占比更多 */ #d1c2bd 60%,
    /* 粉色 */ #f0c6b8 80%,
    /* 橙色 */ #e29f84 100%
  );
  /* max-width: 1200px; */
  /* margin: 0 auto; */
  padding: 10px 50px 20px 50px;
  position: relative;
  min-height: 600px;
  height: 100vh;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
}

.logo {
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.logo:hover {
  transform: translateY(-2px);
}

.logo img {
  height: 28px;
  filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));
}

.nav-links {
  display: flex;
  gap: 15px;
  align-items: center;
}

.nav-links a {
  text-decoration: none;
  color: #1a365d;
  font-size: 14px;
  font-weight: 500;
  padding: 6px 12px;
  transition: all 0.3s ease;
  position: relative;
}

.nav-links a:not(.login-btn):hover {
  color: #4a5568;
}

.nav-links a:not(.login-btn):hover::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: linear-gradient(to right, #d1c2bd, #f0c6b8);
  border-radius: 1px;
}

.login-btn {
  background: rgba(255, 255, 255, 0.25);
  color: #1a365d;
  padding: 6px 16px;
  border-radius: 20px;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
}

.login-btn:hover {
  background: rgba(255, 255, 255, 0.35);
}
</style>
